// 交互式绘本主容器组件
// 使用LIBLIB AI进行图片生成

import { useState, useEffect } from 'react';
import storyData from '../data/storyData';
import storyDataEn from '../data/storyDataEn';
import { StoryPage } from './StoryPage';
import { StoryGenerator } from './StoryGenerator';
import { StorySelector } from './StorySelector';
import { LanguageSettings } from './LanguageSettings';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Input } from './ui/input';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import i18nService, { Language } from '../services/i18nService';
import speechService from '../services/speechService';
import liblibService from '../services/liblibService';
import storyManager from '../services/storyManager';
import storyGeneratorService from '../services/storyGeneratorService';
import openAIService from '../services/openAIService';
import '../services/liblibServiceDebug.js'; // 导入调试工具
import { clearAllImageCache, clearOldSessionCache } from '../services/illustrationGenerator';

interface UserResponse {
  pageId: number;
  response: string;
}

export function StoryContainer() {
  const [currentPageIndex, setCurrentPageIndex] = useState(0);
  const [userResponses, setUserResponses] = useState<UserResponse[]>([]);
  const [showIntro, setShowIntro] = useState(true);
  const [showReport, setShowReport] = useState(false);
  const [showStoryGenerator, setShowStoryGenerator] = useState(false);
  const [showStorySelector, setShowStorySelector] = useState(false);
  const [showLanguageSettings, setShowLanguageSettings] = useState(false);
  const [analysisReport, setAnalysisReport] = useState<any>(null);
  const [currentLanguage, setCurrentLanguage] = useState<Language>(i18nService.getCurrentLanguage());
  const [currentStoryData, setCurrentStoryData] = useState(
    i18nService.getCurrentLanguage() === 'zh' ? storyData : storyDataEn
  );

  const totalPages = currentStoryData.pages.length;
  const progress = ((currentPageIndex + 1) / totalPages) * 100;
  const currentPage = currentStoryData.pages[currentPageIndex];

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = (language: Language) => {
      setCurrentLanguage(language);
      speechService.setLanguage(language);

      // 切换故事数据
      const newStoryData = language === 'zh' ? storyData : storyDataEn;
      setCurrentStoryData(newStoryData);

      // 重置阅读状态
      setCurrentPageIndex(0);
      setUserResponses([]);
      setShowReport(false);
    };

    i18nService.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18nService.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);

  // 初始化API密钥
  useEffect(() => {
    // 检查是否需要清除缓存（只在新会话开始时清除）
    const lastSessionTime = localStorage.getItem('last_session_time');
    const currentTime = Date.now();
    const sessionTimeout = 30 * 60 * 1000; // 30分钟会话超时

    if (!lastSessionTime || (currentTime - parseInt(lastSessionTime)) > sessionTimeout) {
      console.log('🧹 检测到新会话或会话超时，清除旧的图片缓存...');
      clearAllImageCache();
      localStorage.setItem('last_session_time', currentTime.toString());
      console.log('✅ 已清除旧的图片缓存');
    } else {
      console.log('🔄 继续当前会话，清理过期缓存...');
      // 清理超过30分钟的旧缓存，但保留当前会话的缓存
      clearOldSessionCache();
    }

    // 初始化LiblibAI API密钥
    const liblibAccessKey = import.meta.env.VITE_LIBLIB_ACCESS_KEY;
    const liblibSecretKey = import.meta.env.VITE_LIBLIB_SECRET_KEY;

    if (liblibAccessKey && liblibSecretKey) {
      try {
        liblibService.initializeApiKeys(liblibAccessKey, liblibSecretKey);
        console.log('✅ LiblibAI API密钥初始化成功');
        console.log('🔑 AccessKey:', liblibAccessKey.substring(0, 10) + '...');

        // 验证API状态
        const status = liblibService.getApiStatus();
        console.log('📊 LiblibAI API状态:', status);
      } catch (error) {
        console.error('❌ LiblibAI API密钥初始化失败:', error);
      }
    } else {
      console.warn('⚠️ LiblibAI API密钥未在环境变量中找到');
      console.log('请确保.env文件中包含:');
      console.log('VITE_LIBLIB_ACCESS_KEY=your_access_key');
      console.log('VITE_LIBLIB_SECRET_KEY=your_secret_key');
    }

    // 初始化OpenAI API密钥
    const openaiApiKey = import.meta.env.VITE_OPENAI_API_KEY;

    if (openaiApiKey && openaiApiKey !== 'your_openai_api_key_here') {
      try {
        storyGeneratorService.initializeApiKey(openaiApiKey);
        console.log('✅ OpenAI API密钥初始化成功');
        console.log('🔑 API Key:', openaiApiKey.substring(0, 10) + '...');
      } catch (error) {
        console.error('❌ OpenAI API密钥初始化失败:', error);
      }
    } else {
      console.warn('⚠️ OpenAI API密钥未在环境变量中找到或未设置');
      console.log('请在.env文件中设置:');
      console.log('VITE_OPENAI_API_KEY=your_openai_api_key');
    }
  }, []);

  const handleNext = () => {
    // 确保在翻页时停止当前页面的语音
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }

    if (currentPageIndex < totalPages - 1) {
      setCurrentPageIndex(currentPageIndex + 1);
      window.scrollTo(0, 0);
    } else {
      generateReport();
    }
  };

  const handlePrevious = () => {
    // 确保在翻页时停止当前页面的语音
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }

    if (currentPageIndex > 0) {
      setCurrentPageIndex(currentPageIndex - 1);
      window.scrollTo(0, 0);
    }
  };

  const handleResponseSubmit = (pageId: number, response: string) => {
    // 检查是否已有该页面的回答
    const existingResponseIndex = userResponses.findIndex(r => r.pageId === pageId);

    if (existingResponseIndex >= 0) {
      // 更新现有回答
      const updatedResponses = [...userResponses];
      updatedResponses[existingResponseIndex] = { pageId, response };
      setUserResponses(updatedResponses);
    } else {
      // 添加新回答
      setUserResponses([...userResponses, { pageId, response }]);
    }
  };

  const startStory = () => {
    setShowIntro(false);
  };

  const generateReport = async () => {
    console.log('📊 开始生成分析报告...');

    const interactivePages = currentStoryData.pages.filter(page => page.isInteractive);
    const completedInteractions = userResponses.length;

    // 准备问题和回答数据
    const questions = interactivePages.map(page => page.question || page.interactiveQuestion || '');
    const answers = userResponses.map(response => response.response);

    console.log('📝 交互数据:', { questions, answers });

    try {
      // 检查OpenAI服务是否可用
      console.log('🔍 检查OpenAI服务状态...');
      const isOpenAIAvailable = openAIService.isApiKeyInitialized();
      console.log('OpenAI API密钥状态:', isOpenAIAvailable);

      if (!isOpenAIAvailable) {
        console.warn('⚠️ OpenAI API密钥未设置，直接使用本地分析');
        generateLocalReport(completedInteractions, interactivePages.length);
        return;
      }

      // 尝试使用OpenAI增强版专业分析
      console.log('🤖 尝试使用OpenAI增强版专业分析...');
      console.log('📝 分析数据:', {
        questionsCount: questions.length,
        answersCount: answers.length,
        theme: currentStoryData.theme,
        storyTitle: currentStoryData.title,
        hasCharacters: !!currentStoryData.characters,
        charactersCount: currentStoryData.characters?.length || 0,
        questions: questions.slice(0, 2), // 只显示前两个问题用于调试
        answers: answers.slice(0, 2) // 只显示前两个回答用于调试
      });

      // 优先使用增强版分析方法（如果可用）
      let openAIAnalysis;
      if (typeof openAIService.analyzeAutismPerformanceWithContext === 'function') {
        console.log('✅ 使用增强版分析方法（包含完整上下文）');
        openAIAnalysis = await openAIService.analyzeAutismPerformanceWithContext(
          questions,
          answers,
          currentStoryData,
          userResponses
        );
      } else {
        console.log('⚠️ 回退到标准分析方法');
        openAIAnalysis = await openAIService.analyzeAutismPerformance(
          questions,
          answers,
          currentStoryData.theme
        );
      }

      console.log('✅ OpenAI专业分析完成，结果:', openAIAnalysis);

      // 转换OpenAI分析结果为报告格式
      const report = convertOpenAIAnalysisToReport(openAIAnalysis, completedInteractions, interactivePages.length);

      setAnalysisReport(report);
      setShowReport(true);

    } catch (error) {
      console.error('❌ OpenAI分析失败，回退到本地分析:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack?.substring(0, 500)
      });

      // 回退到本地分析
      generateLocalReport(completedInteractions, interactivePages.length);
    }
  };

  // 转换OpenAI分析结果为报告格式
  const convertOpenAIAnalysisToReport = (openAIAnalysis: any, completedInteractions: number, totalInteractions: number) => {
    console.log('🔄 转换OpenAI分析结果...');

    // 检查是否是高级分析结果
    if (openAIAnalysis.detailed_scores) {
      // 高级分析结果
      return {
        completedInteractions,
        totalInteractions,
        analysisType: 'openai_advanced',
        analysisDate: openAIAnalysis.analysis_metadata?.analysis_date,
        scores: {
          languageVocabulary: openAIAnalysis.detailed_scores.language_expression?.score || 3,
          logicalThinking: openAIAnalysis.detailed_scores.logical_reasoning?.score || 3,
          socialAdaptation: openAIAnalysis.detailed_scores.social_cognition?.score || 3,
          emotionalRecognition: openAIAnalysis.detailed_scores.emotional_regulation?.score || 3,
          selfReflection: openAIAnalysis.detailed_scores.self_reflection?.score || 3
        },
        recommendations: {
          languageVocabulary: openAIAnalysis.detailed_scores.language_expression?.analysis || '语言表达能力分析',
          logicalThinking: openAIAnalysis.detailed_scores.logical_reasoning?.analysis || '逻辑推理能力分析',
          socialAdaptation: openAIAnalysis.detailed_scores.social_cognition?.analysis || '社交认知能力分析',
          emotionalRecognition: openAIAnalysis.detailed_scores.emotional_regulation?.analysis || '情感调节能力分析',
          overall: openAIAnalysis.assessment_summary?.overall_performance || '整体表现良好'
        },
        professionalAnalysis: {
          keyStrengths: openAIAnalysis.assessment_summary?.key_strengths || [],
          areasForImprovement: openAIAnalysis.assessment_summary?.areas_for_improvement || [],
          interventionPlan: openAIAnalysis.intervention_plan || null,
          professionalNotes: openAIAnalysis.professional_notes || null
        }
      };
    } else if (openAIAnalysis.scores) {
      // 基础分析结果
      return {
        completedInteractions,
        totalInteractions,
        analysisType: 'openai_basic',
        scores: {
          languageVocabulary: openAIAnalysis.scores.language_vocabulary || 3,
          logicalThinking: openAIAnalysis.scores.logical_thinking || 3,
          socialAdaptation: openAIAnalysis.scores.social_adaptation || 3,
          emotionalRecognition: openAIAnalysis.scores.emotional_recognition || 3
        },
        recommendations: {
          languageVocabulary: openAIAnalysis.analysis?.language_vocabulary || '语言词汇量分析',
          logicalThinking: openAIAnalysis.analysis?.logical_thinking || '逻辑思维分析',
          socialAdaptation: openAIAnalysis.analysis?.social_adaptation || '社会适应分析',
          emotionalRecognition: openAIAnalysis.analysis?.emotional_recognition || '情感识别分析',
          overall: openAIAnalysis.overall_recommendation || '继续加强练习'
        }
      };
    } else {
      throw new Error('OpenAI分析结果格式不正确');
    }
  };

  // 本地分析方法（回退选项）
  const generateLocalReport = (completedInteractions: number, totalInteractions: number) => {
    console.log('🔄 使用本地分析方法...');

    // 原有的本地分析逻辑
    let languageScore = 0;
    let logicScore = 0;
    let socialScore = 0;
    let emotionalScore = 0;

    userResponses.forEach(response => {
      const text = response.response.toLowerCase();
      const words = text.split(/\s+/);
      const uniqueWords = new Set(words);

      languageScore += Math.min(words.length / 5, 5) + Math.min(uniqueWords.size / 3, 5);

      const logicWords = ["因为", "所以", "如果", "但是", "然后", "接着", "首先", "其次", "最后"];
      const logicCount = logicWords.filter(word => text.includes(word)).length;
      logicScore += Math.min(logicCount * 2, 5);

      const socialWords = ["朋友", "一起", "帮助", "分享", "谢谢", "请", "对不起", "合作", "玩"];
      const socialCount = socialWords.filter(word => text.includes(word)).length;
      socialScore += Math.min(socialCount * 2, 5);

      const emotionWords = ["高兴", "难过", "害怕", "生气", "担心", "开心", "喜欢", "爱", "紧张", "兴奋"];
      const emotionCount = emotionWords.filter(word => text.includes(word)).length;
      emotionalScore += Math.min(emotionCount * 2, 5);
    });

    const normalizeScore = (score: number) => {
      if (completedInteractions === 0) return 0;
      return Math.min(Math.round((score / completedInteractions) * 10) / 10, 5);
    };

    const report = {
      completedInteractions,
      totalInteractions,
      analysisType: 'local',
      scores: {
        languageVocabulary: normalizeScore(languageScore),
        logicalThinking: normalizeScore(logicScore),
        socialAdaptation: normalizeScore(socialScore),
        emotionalRecognition: normalizeScore(emotionalScore)
      },
      recommendations: generateRecommendations(
        normalizeScore(languageScore),
        normalizeScore(logicScore),
        normalizeScore(socialScore),
        normalizeScore(emotionalScore)
      )
    };

    setAnalysisReport(report);
    setShowReport(true);
  };

  const generateRecommendations = (
    languageScore: number,
    logicScore: number,
    socialScore: number,
    emotionalScore: number
  ) => {
    const recommendations = {
      languageVocabulary: '',
      logicalThinking: '',
      socialAdaptation: '',
      emotionalRecognition: '',
      overall: ''
    };

    // 语言建议
    if (languageScore < 2) {
      recommendations.languageVocabulary = "词汇量较为有限，表达方式简单。建议通过更多的阅读和对话活动，扩展孩子的词汇库。";
    } else if (languageScore < 4) {
      recommendations.languageVocabulary = "具备基本的词汇表达能力，能够使用简单句型进行交流。建议鼓励使用更丰富的形容词和动词。";
    } else {
      recommendations.languageVocabulary = "词汇量丰富，能够使用多样化的词汇进行表达。建议继续通过阅读拓展专业领域词汇。";
    }

    // 逻辑建议
    if (logicScore < 2) {
      recommendations.logicalThinking = "逻辑表达能力需要加强，因果关系理解有限。建议通过简单的推理游戏培养逻辑思维能力。";
    } else if (logicScore < 4) {
      recommendations.logicalThinking = "能够理解基本的因果关系，表达有一定的逻辑性。建议通过更复杂的问题解决活动提升逻辑思维。";
    } else {
      recommendations.logicalThinking = "逻辑思维能力较强，能够清晰地表达因果关系和推理过程。建议尝试更复杂的逻辑推理活动。";
    }

    // 社交建议
    if (socialScore < 2) {
      recommendations.socialAdaptation = "社交互动意识较弱，对社交规则理解有限。建议通过角色扮演游戏培养基本社交技能。";
    } else if (socialScore < 4) {
      recommendations.socialAdaptation = "具备基本的社交意识，能够理解简单的社交规则。建议增加小组活动，提升社交互动能力。";
    } else {
      recommendations.socialAdaptation = "社交适应能力良好，能够理解并应用社交规则。建议参与更多团体活动，进一步提升社交能力。";
    }

    // 情感建议
    if (emotionalScore < 2) {
      recommendations.emotionalRecognition = "情感识别和表达能力有限，难以准确表达自身情感。建议通过情绪卡片游戏增强情感识别能力。";
    } else if (emotionalScore < 4) {
      recommendations.emotionalRecognition = "能够识别基本情绪，有一定的情感表达能力。建议通过讨论故事人物情感，提升情感理解深度。";
    } else {
      recommendations.emotionalRecognition = "情感识别能力较强，能够准确表达和理解多种情绪。建议探索更复杂的情感状态和共情能力培养。";
    }

    // 整体建议
    const avgScore = (languageScore + logicScore + socialScore + emotionalScore) / 4;
    if (avgScore < 2) {
      recommendations.overall = "建议增加日常交流互动，使用简单明确的语言，配合视觉提示辅助理解。可以通过结构化的社交故事和游戏，逐步提升语言表达和社交能力。";
    } else if (avgScore < 4) {
      recommendations.overall = "孩子具备基本的交流能力，建议通过更多的小组活动和角色扮演，提升社交互动质量。同时，可以引导孩子表达更复杂的情感和想法，培养共情能力。";
    } else {
      recommendations.overall = "孩子在语言交流方面表现良好，建议提供更具挑战性的社交情境，如解决冲突、协商合作等，进一步提升高阶社交能力和情感表达深度。";
    }

    return recommendations;
  };

  const restartStory = () => {
    console.log('🔄 重新开始阅读，清除所有图片缓存...');

    // 清除所有生成的图片缓存，确保新的交互可以生成新的插画
    clearAllImageCache();

    // 更新会话时间，标记为新的阅读会话
    localStorage.setItem('last_session_time', Date.now().toString());

    // 重置状态
    setCurrentPageIndex(0);
    setUserResponses([]);
    setShowReport(false);
    setShowIntro(true);
    setShowStoryGenerator(false);
    setShowStorySelector(false);

    console.log('✅ 已清除所有图片缓存，开始新的阅读体验');
  };

  // 处理故事生成
  const handleStoryGenerated = (newStoryData: any) => {
    console.log('🎉 新故事生成完成:', newStoryData.title);

    // 添加故事到管理器
    const storyId = storyManager.addStory(newStoryData);

    // 切换到新故事
    storyManager.switchToStory(storyId);
    setCurrentStoryData(newStoryData);

    // 重置阅读状态
    setCurrentPageIndex(0);
    setUserResponses([]);
    setShowStoryGenerator(false);
    setShowIntro(true);

    // 清除图片缓存，为新故事准备
    clearAllImageCache();
  };

  // 处理故事选择
  const handleStorySelected = (selectedStoryData: any) => {
    console.log('📖 切换到故事:', selectedStoryData.title);

    setCurrentStoryData(selectedStoryData);

    // 重置阅读状态
    setCurrentPageIndex(0);
    setUserResponses([]);
    setShowStorySelector(false);
    setShowIntro(true);

    // 清除图片缓存，为新故事准备
    clearAllImageCache();
  };

  // 显示故事生成器
  const showStoryGeneratorView = () => {
    setShowStoryGenerator(true);
    setShowIntro(false);
  };

  // 显示故事选择器
  const showStorySelectorView = () => {
    setShowStorySelector(true);
    setShowIntro(false);
  };

  // 取消故事生成/选择
  const cancelStoryOperation = () => {
    setShowStoryGenerator(false);
    setShowStorySelector(false);
    setShowIntro(true);
  };

  // 返回主页面
  const handleReturnHome = () => {
    console.log('🏠 返回主页面，保存当前阅读进度...');

    // 停止当前的语音播放
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }

    // 保存当前阅读进度（如果需要的话）
    // 这里可以添加保存进度的逻辑

    // 重置到主页面状态
    setShowIntro(true);
    setShowReport(false);
    setShowStoryGenerator(false);
    setShowStorySelector(false);

    // 滚动到页面顶部
    window.scrollTo(0, 0);

    console.log('✅ 已返回主页面');
  };

  // 显示语言设置
  if (showLanguageSettings) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
        <LanguageSettings onClose={() => setShowLanguageSettings(false)} />
      </div>
    );
  }

  if (showIntro) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
        <Card className="max-w-2xl w-full">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="text-3xl text-center flex-1">
                🐻 {currentStoryData.title}
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowLanguageSettings(true)}
                className="ml-2"
              >
                ⚙️ {i18nService.t('settings')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
          <div className="mb-6">
            <p className="text-lg mb-4">
              {currentLanguage === 'zh'
                ? `这是一个为${currentStoryData.ageGroup}自闭症儿童设计的交互式绘本，主题是"${currentStoryData.theme}"。`
                : `This is an interactive storybook designed for ${currentStoryData.ageGroup} children with autism spectrum disorder, themed "${currentStoryData.theme}".`
              }
            </p>
            <p className="mb-4">
              {currentLanguage === 'zh'
                ? '在这个故事中，你将跟随主角的冒险，学习重要的生活知识。故事中有3个交互环节，你需要回答问题，帮助主角做出选择。'
                : 'In this story, you will follow the protagonist\'s adventure and learn important life knowledge. There are 3 interactive sessions in the story where you need to answer questions and help the protagonist make choices.'
              }
            </p>
            <p className="mb-4">
              {currentLanguage === 'zh'
                ? '完成所有交互后，系统会生成一份评估报告，分析你在语言词汇量、思维逻辑、社会适应和情感识别四个维度的表现。'
                : 'After completing all interactions, the system will generate an assessment report analyzing your performance in four dimensions: language vocabulary, logical thinking, social adaptation, and emotional recognition.'
              }
            </p>
            <p className="mb-4 font-semibold text-blue-600">
              {currentLanguage === 'zh'
                ? '新功能：本绘本支持语音朗读和语音输入，让体验更加便捷！'
                : 'New Feature: This storybook supports voice reading and voice input for a more convenient experience!'
              }
            </p>
            <p className="mb-4 font-semibold text-green-600">
              {currentLanguage === 'zh'
                ? '✨ 集成LIBLIB AI图片生成，为您的回答创造个性化插画！'
                : '✨ Integrated LIBLIB AI image generation to create personalized illustrations for your answers!'
              }
            </p>
            <p className="mb-4 font-semibold text-purple-600">
              {currentLanguage === 'zh'
                ? '🎨 全新AI故事生成功能：可生成人际关系、家庭生活、法律常识、人伦道德四个主题的故事！'
                : '🎨 New AI story generation feature: Generate stories on four themes - interpersonal relationships, family life, legal knowledge, and moral ethics!'
              }
            </p>
          </div>
            <div className="flex justify-center gap-3 flex-wrap">
              <Button onClick={startStory} size="lg">
                📚 {currentLanguage === 'zh' ? '开始阅读' : 'Start Reading'}
              </Button>
              <Button onClick={showStorySelectorView} variant="outline" size="lg">
                📖 {currentLanguage === 'zh' ? '选择故事' : 'Select Story'}
              </Button>
              <Button onClick={showStoryGeneratorView} variant="outline" size="lg">
                🎨 {currentLanguage === 'zh' ? '生成新故事' : 'Generate New Story'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 故事生成器视图
  if (showStoryGenerator) {
    return (
      <StoryGenerator
        onStoryGenerated={handleStoryGenerated}
        onCancel={cancelStoryOperation}
      />
    );
  }

  // 故事选择器视图
  if (showStorySelector) {
    return (
      <StorySelector
        onStorySelected={handleStorySelected}
        onGenerateNew={showStoryGeneratorView}
        onCancel={cancelStoryOperation}
      />
    );
  }

  if (showReport) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
        <Card className="max-w-3xl w-full">
          <CardHeader>
            <CardTitle className="text-3xl text-center">
              📊 {currentLanguage === 'zh'
                ? '自闭症儿童语言交互能力评估报告'
                : 'Autism Spectrum Disorder Language Interaction Assessment Report'
              }
            </CardTitle>
          </CardHeader>
          <CardContent>

          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">
              {currentLanguage === 'zh' ? '基本信息' : 'Basic Information'}
            </h2>
            <p>
              {currentLanguage === 'zh' ? '故事标题：' : 'Story Title: '}{currentStoryData.title}
            </p>
            <p>
              {currentLanguage === 'zh' ? '年龄段：' : 'Age Group: '}{currentStoryData.ageGroup}
            </p>
            <p>
              {currentLanguage === 'zh' ? '绘本主题：' : 'Theme: '}{currentStoryData.theme}
            </p>
            <p>
              {currentLanguage === 'zh'
                ? `完成交互环节数量：${analysisReport.completedInteractions}/${analysisReport.totalInteractions}`
                : `Completed Interactions: ${analysisReport.completedInteractions}/${analysisReport.totalInteractions}`
              }
            </p>
          </div>

          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">
              {currentLanguage === 'zh'
                ? '能力维度评估（满分5分）'
                : 'Ability Assessment (Max 5 points)'
              }
            </h2>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span>
                    {currentLanguage === 'zh'
                      ? `语言词汇量：${analysisReport.scores.languageVocabulary}分`
                      : `Language Vocabulary: ${analysisReport.scores.languageVocabulary} points`
                    }
                  </span>
                  <span>{analysisReport.scores.languageVocabulary}/5</span>
                </div>
                <Progress value={analysisReport.scores.languageVocabulary * 20} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>
                    {currentLanguage === 'zh'
                      ? `思维逻辑：${analysisReport.scores.logicalThinking}分`
                      : `Logical Thinking: ${analysisReport.scores.logicalThinking} points`
                    }
                  </span>
                  <span>{analysisReport.scores.logicalThinking}/5</span>
                </div>
                <Progress value={analysisReport.scores.logicalThinking * 20} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>
                    {currentLanguage === 'zh'
                      ? `社会适应：${analysisReport.scores.socialAdaptation}分`
                      : `Social Adaptation: ${analysisReport.scores.socialAdaptation} points`
                    }
                  </span>
                  <span>{analysisReport.scores.socialAdaptation}/5</span>
                </div>
                <Progress value={analysisReport.scores.socialAdaptation * 20} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span>
                    {currentLanguage === 'zh'
                      ? `情感识别：${analysisReport.scores.emotionalRecognition}分`
                      : `Emotional Recognition: ${analysisReport.scores.emotionalRecognition} points`
                    }
                  </span>
                  <span>{analysisReport.scores.emotionalRecognition}/5</span>
                </div>
                <Progress value={analysisReport.scores.emotionalRecognition * 20} className="h-2" />
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">
              {currentLanguage === 'zh' ? '详细分析' : 'Detailed Analysis'}
            </h2>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">
                  {currentLanguage === 'zh' ? '语言词汇量' : 'Language Vocabulary'}
                </h3>
                <p>{analysisReport.recommendations.languageVocabulary}</p>
              </div>
              <div>
                <h3 className="font-medium">
                  {currentLanguage === 'zh' ? '思维逻辑' : 'Logical Thinking'}
                </h3>
                <p>{analysisReport.recommendations.logicalThinking}</p>
              </div>
              <div>
                <h3 className="font-medium">
                  {currentLanguage === 'zh' ? '社会适应' : 'Social Adaptation'}
                </h3>
                <p>{analysisReport.recommendations.socialAdaptation}</p>
              </div>
              <div>
                <h3 className="font-medium">
                  {currentLanguage === 'zh' ? '情感识别' : 'Emotional Recognition'}
                </h3>
                <p>{analysisReport.recommendations.emotionalRecognition}</p>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">
              {currentLanguage === 'zh' ? '总结建议' : 'Summary Recommendations'}
            </h2>
            <p>{analysisReport.recommendations.overall}</p>
          </div>

            <div className="flex justify-center">
              <Button onClick={restartStory} size="lg">
                🔄 {i18nService.t('restart')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-amber-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-center">
              🐻 {currentStoryData.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  📄 {i18nService.t('page')} {currentPageIndex + 1} {i18nService.t('of')} {totalPages} {currentLanguage === 'zh' ? '页' : 'pages'}
                </span>
                {currentPage.isInteractive && (
                  <Badge variant="outline" className="text-xs">
                    {currentLanguage === 'zh' ? '交互页面' : 'Interactive'}
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  📈 {Math.round(progress)}%
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowLanguageSettings(true)}
                >
                  ⚙️
                </Button>
              </div>
            </div>
            <Progress value={progress} className="h-3" />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>
                {currentLanguage === 'zh'
                  ? `已完成 ${userResponses.length} 个交互`
                  : `Completed ${userResponses.length} interactions`
                }
              </span>
              <span>
                {currentLanguage === 'zh'
                  ? `剩余 ${totalPages - currentPageIndex - 1} 页`
                  : `${totalPages - currentPageIndex - 1} pages remaining`
                }
              </span>
            </div>
          </CardContent>
        </Card>

        <StoryPage
          page={currentPage}
          onNext={handleNext}
          onResponseSubmit={handleResponseSubmit}
          onReturnHome={handleReturnHome}
          userResponses={userResponses}
          storyData={currentStoryData}
        />

        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentPageIndex === 0}
              >
                ⬅️ {currentLanguage === 'zh' ? '上一页' : 'Previous'}
              </Button>

              {!currentPage.isInteractive && (
                <Button
                  onClick={handleNext}
                  disabled={currentPageIndex === totalPages - 1}
                >
                  {currentPageIndex === totalPages - 1
                    ? (currentLanguage === 'zh' ? '🎉 完成阅读' : '🎉 Complete Reading')
                    : (currentLanguage === 'zh' ? '➡️ 下一页' : '➡️ Next')
                  }
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
